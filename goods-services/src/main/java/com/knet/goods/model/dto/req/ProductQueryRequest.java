package com.knet.goods.model.dto.req;

import com.knet.common.base.BasePageRequest;
import com.knet.common.enums.ProductMark;
import com.knet.common.enums.SortByFileds;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/19 14:04
 * @description: 商品查询请求体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProductQueryRequest extends BasePageRequest {
    @Schema(description = "sku", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String sku;

    @Schema(description = "品牌", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String brand;

    @Schema(description = "账号", requiredMode = Schema.RequiredMode.NOT_REQUIRED, hidden = true)
    private String account;

    /**
     * @see SortByFileds 排序字段
     */
    @Schema(description = "排序字段", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private SortByFileds sortBy;

    /**
     * @see ProductMark 商品标识
     */
    @Schema(description = "商品标识，HOT_SALE NEW COMMON 查询全部 不传值 ", example = "COMMON", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private ProductMark mark;

    @Schema(description = "sku列表", requiredMode = Schema.RequiredMode.NOT_REQUIRED, hidden = true)
    private List<String> skus;
    
    @Schema(description = "商品名称列表", requiredMode = Schema.RequiredMode.NOT_REQUIRED, hidden = true)
    private List<String> remarks;

    @Schema(description = "尺码筛选", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String spec;

    @Schema(description = "商品数量最小值", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer minTotal;

    @Schema(description = "商品数量最大值", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer maxTotal;
}
