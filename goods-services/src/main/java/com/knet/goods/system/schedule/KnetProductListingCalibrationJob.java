package com.knet.goods.system.schedule;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.knet.common.exception.ServiceException;
import com.knet.goods.model.dto.third.req.KnetGroupGetPlatformListingReq;
import com.knet.goods.model.dto.third.resp.KnetPlatformListingQueryVo;
import com.knet.goods.service.IKnetProductService;
import com.knet.goods.service.IThirdApiService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/30 10:13
 * @description: 校准knet_listing
 */
@Slf4j
@Component
public class KnetProductListingCalibrationJob {
    @Resource
    private IThirdApiService thirdApiService;
    @Resource
    private IKnetProductService knetProductService;

    /**
     * 暂定 6小时执行一次
     * 同步KG平台寄售单数据，下架本地不存在于KG平台的商品
     * <p>
     * 使用默认参数执行：每页100条数据，最大处理1000页
     */
    @XxlJob("syncListingFromKg")
    public ReturnT<String> syncInventoryFromKgBySku() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("同步KG平台寄售单数据，下架本地不存在于KG平台的商品");
        try {
            JobParams params = new JobParams();
            log.info("开始执行KG平台寄售单校准任务，使用默认参数: pageSize={}, maxPages={}", params.pageSize, params.maxPages);
            XxlJobHelper.log("开始执行KG平台寄售单校准任务，使用默认参数: pageSize={}, maxPages={}", params.pageSize, params.maxPages);
            // 1. 分页获取KG平台所有有效的oneId
            Set<String> kgValidOneIds = getAllValidOneIdsFromKg(params);
            if (CollUtil.isEmpty(kgValidOneIds)) {
                String message = "从KG平台未获取到任何有效的oneId数据";
                log.warn(message);
                XxlJobHelper.log(message);
                return ReturnT.SUCCESS;
            }
            log.info("从KG平台获取到 {} 个有效的oneId", kgValidOneIds.size());
            XxlJobHelper.log("从KG平台获取到 {} 个有效的oneId", kgValidOneIds.size());
            // 2. 查询本地所有上架状态的商品oneId
            Set<String> localOnSaleOneIds = knetProductService.getLocalOnSaleOneIds();
            log.info("本地上架状态商品共 {} 个", localOnSaleOneIds.size());
            XxlJobHelper.log("本地上架状态商品共 {} 个", localOnSaleOneIds.size());
            // 3. 找出需要下架的oneId（本地有但KG平台没有的）
            Set<String> oneIdsToOffSale = new HashSet<>(localOnSaleOneIds);
            oneIdsToOffSale.removeAll(kgValidOneIds);
            if (CollUtil.isEmpty(oneIdsToOffSale)) {
                String message = "没有需要下架的商品";
                log.info(message);
                XxlJobHelper.log(message);
                return ReturnT.SUCCESS;
            }
            log.info("发现 {} 个商品需要下架，商品ID列表: {}", oneIdsToOffSale.size(), oneIdsToOffSale);
            XxlJobHelper.log("发现 {} 个商品需要下架", oneIdsToOffSale.size());
            // 4. 执行批量下架操作
            int updatedCount = knetProductService.updateExistingProductsToOffSale(new ArrayList<>(oneIdsToOffSale));
            stopWatch.stop();
            String successMessage = String.format("KG平台寄售单校准任务执行完成，耗时: %d ms，成功下架 %d 个商品", stopWatch.getTotalTimeMillis(), updatedCount);
            log.info(successMessage);
            XxlJobHelper.log(successMessage);
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            stopWatch.stop();
            String errorMessage = String.format("KG平台寄售单校准任务执行失败，耗时: %d ms，错误信息: %s", stopWatch.getTotalTimeMillis(), e.getMessage());
            log.error(errorMessage, e);
            XxlJobHelper.log(errorMessage);
            return new ReturnT<>(ReturnT.FAIL_CODE, errorMessage);
        }
    }

    /**
     * 分页获取KG平台所有有效的oneId
     *
     * @param params 任务参数
     * @return 所有有效的oneId集合
     */
    private Set<String> getAllValidOneIdsFromKg(JobParams params) {
        Set<String> allValidOneIds = new HashSet<>();
        int currentPage = 1;
        long totalRecords = 0;
        long totalPages = 0;
        boolean isFirstPage = true;
        while (true) {
            try {
                KnetGroupGetPlatformListingReq req = KnetGroupGetPlatformListingReq.crateB2bShopReq(currentPage, params.pageSize);
                log.info("正在获取KG平台数据，第 {} 页，每页 {} 条", currentPage, params.pageSize);
                XxlJobHelper.log("正在获取KG平台数据，第 {} 页，每页 {} 条", currentPage, params.pageSize);
                IPage<KnetPlatformListingQueryVo> pageResult = thirdApiService.queryPlatformListings(req);
                if (pageResult == null || CollUtil.isEmpty(pageResult.getRecords())) {
                    log.info("第 {} 页没有数据，结束分页获取", currentPage);
                    XxlJobHelper.log("第 {} 页没有数据，结束分页获取", currentPage);
                    break;
                }
                // 第一页时记录总数信息
                if (isFirstPage) {
                    totalRecords = pageResult.getTotal();
                    totalPages = pageResult.getPages();
                    log.info("KG平台总共有 {} 条记录，分为 {} 页", totalRecords, totalPages);
                    XxlJobHelper.log("KG平台总共有 {} 条记录，分为 {} 页", totalRecords, totalPages);
                    isFirstPage = false;
                    // 检查是否超过最大页数限制
                    if (totalPages > params.maxPages) {
                        log.warn("总页数 {} 超过最大处理页数限制 {}，将只处理前 {} 页", totalPages, params.maxPages, params.maxPages);
                        XxlJobHelper.log("总页数 {} 超过最大处理页数限制 {}，将只处理前 {} 页", totalPages, params.maxPages, params.maxPages);
                    }
                }
                List<KnetPlatformListingQueryVo> pageData = pageResult.getRecords();
                // 提取oneId并过滤空值
                Set<String> pageOneIds = pageData
                        .stream()
                        .map(KnetPlatformListingQueryVo::getOneId)
                        .filter(StrUtil::isNotBlank)
                        .collect(Collectors.toSet());
                allValidOneIds.addAll(pageOneIds);
                log.info("第 {} 页获取到 {} 条数据，有效oneId {} 个，累计 {} 个，进度: {}/{}",
                        currentPage, pageData.size(), pageOneIds.size(), allValidOneIds.size(), currentPage, totalPages);
                XxlJobHelper.log("第 {} 页获取到 {} 条数据，有效oneId {} 个，累计 {} 个，进度: {}/{}",
                        currentPage, pageData.size(), pageOneIds.size(), allValidOneIds.size(), currentPage, totalPages);
                // 检查是否已经是最后一页或达到最大页数限制
                if (currentPage >= totalPages || currentPage >= params.maxPages) {
                    if (currentPage >= params.maxPages) {
                        log.warn("已达到最大处理页数限制 {}，停止获取更多数据", params.maxPages);
                        XxlJobHelper.log("已达到最大处理页数限制 {}，停止获取更多数据", params.maxPages);
                    } else {
                        log.info("已处理完所有 {} 页数据", totalPages);
                        XxlJobHelper.log("已处理完所有 {} 页数据", totalPages);
                    }
                    break;
                }
                currentPage++;
                // 添加短暂延迟，避免对KG平台造成过大压力
                Thread.sleep(100);
            } catch (Exception e) {
                log.error("获取KG平台第 {} 页数据失败: {}", currentPage, e.getMessage(), e);
                XxlJobHelper.log("获取KG平台第 {} 页数据失败: {}", currentPage, e.getMessage());
                // 如果连续失败次数过多，则终止任务
                if (currentPage > 1) {
                    log.error("获取数据失败，终止任务执行");
                    XxlJobHelper.log("获取数据失败，终止任务执行");
                    throw new ServiceException("获取KG平台数据失败: " + e.getMessage());
                }
                break;
            }
        }
        return allValidOneIds;
    }

    /**
     * 任务参数内部类
     */
    private static class JobParams {
        /**
         * 每页数据量，默认500
         */
        int pageSize = 500;
        /**
         * 最大处理页数，默认1000，防止无限循环
         */
        int maxPages = 1000;
    }
}
