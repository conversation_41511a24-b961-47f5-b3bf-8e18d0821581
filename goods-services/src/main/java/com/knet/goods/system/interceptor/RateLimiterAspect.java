package com.knet.goods.system.interceptor;

import cn.hutool.core.util.StrUtil;
import com.knet.common.annotation.RateLimiter;
import com.knet.common.exception.ServiceException;
import com.knet.common.utils.RedisCacheUtil;
import groovy.util.logging.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.ParameterNameDiscoverer;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

import static com.baomidou.mybatisplus.extension.ddl.DdlScriptErrorHandler.PrintlnLogErrorHandler.log;
import static com.knet.common.constants.SystemConstant.API_RATE_LIMIT;

/**
 * <AUTHOR>
 * @date 2025/2/28 16:55
 * @description: 流量控制切面
 */
@Slf4j
@Aspect
@Component
public class RateLimiterAspect {

    /**
     * SpEL表达式解析器
     */
    private final ExpressionParser parser = new SpelExpressionParser();
    private final ParameterNameDiscoverer paramNameDiscoverer = new DefaultParameterNameDiscoverer();

    @Around("@annotation(rateLimiter)")
    public Object around(ProceedingJoinPoint joinPoint, RateLimiter rateLimiter) throws Throwable {
        // 1. 生成动态限流Key（结合SpEL）
        String dynamicKey = parseKey(rateLimiter.keyExpression(), joinPoint);
        String fullKey = String.format(API_RATE_LIMIT, dynamicKey);
        // 2. 执行令牌桶算法
        boolean allowed = RedisCacheUtil.isAllowed(fullKey, rateLimiter.capacity(), rateLimiter.refillRate());
        if (!allowed) {
            throw new ServiceException(rateLimiter.message());
        }
        return joinPoint.proceed();
    }

    /**
     * 解析SpEL表达式生成动态Key
     */
    private String parseKey(String expression, ProceedingJoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Object[] args = joinPoint.getArgs();
        if (StrUtil.isEmpty(expression)) {
            return signature.getDeclaringTypeName() + ":" + method.getName();
        }
        EvaluationContext context = new StandardEvaluationContext();
        // 获取方法参数名
        String[] paramNames = paramNameDiscoverer.getParameterNames(method);
        if (paramNames != null) {
            for (int i = 0; i < args.length; i++) {
                context.setVariable(paramNames[i], args[i]);
            }
        }
        if (expression.contains("#")) {
            // 解析SpEL表达式
            String parsedKey = parseSpEL(expression, method, args);
            return parsedKey;
        }
        return parseSpEL(expression, method, args);
    }

    /**
     * 解析SpEL表达式
     */
    private String parseSpEL(String spEL, Method method, Object[] args) {
        try {
            // 创建表达式
            Expression expression = parser.parseExpression(spEL);
            // 创建评估上下文
            EvaluationContext context = new StandardEvaluationContext();
            // 获取参数名称
            String[] parameterNames = paramNameDiscoverer.getParameterNames(method);
            if (parameterNames != null) {
                // 将参数添加到上下文
                for (int i = 0; i < parameterNames.length; i++) {
                    context.setVariable(parameterNames[i], args[i]);
                }
            }
            // 评估表达式
            Object value = expression.getValue(context);
            return value != null ? value.toString() : "";
        } catch (Exception e) {
            log.error("解析SpEL表达式失败: {}, {}");
            throw e;
        }
    }
}
