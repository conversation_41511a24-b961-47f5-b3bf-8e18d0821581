package com.knet.common.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2025/7/23
 * @description: 日志数据清理工具，用于过滤敏感信息和控制日志长度
 */
@Slf4j
public class LogSanitizer {

    /**
     * 参数的最大长度
     */
    private static final int MAX_ARGS_LENGTH = 1000;
    /**
     * 结果的最大长度
     */
    private static final int MAX_RESULT_LENGTH = 1000;

    /**
     * 敏感字段关键词
     */
    private static final Set<String> SENSITIVE_KEYWORDS = new HashSet<>(Arrays.asList(
            "password", "pwd", "token", "secret", "key", "authorization",
            "credential", "auth", "session", "cookie", "sign", "signature",
            "cardno", "cardnum", "idcard", "phone", "mobile", "email"
    ));

    /**
     * 敏感信息替换模式
     */
    private static final Pattern SENSITIVE_PATTERN = Pattern.compile(
            "(?i)(password|pwd|token|secret|key|authorization|credential|auth|session|cookie|sign|signature|cardno|cardnum|idcard|phone|mobile|email)\\s*[=:]\\s*[\"']?([^\\s,}\\]\"']+)",
            Pattern.CASE_INSENSITIVE
    );

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 清理参数数组
     *
     * @param args 参数数组
     * @return 清理后的字符串
     */
    public static String sanitizeArgs(Object[] args) {
        if (args == null || args.length == 0) {
            return "[]";
        }

        try {
            StringBuilder sb = new StringBuilder("[");
            for (int i = 0; i < args.length; i++) {
                if (i > 0) {
                    sb.append(", ");
                }
                sb.append(sanitizeObject(args[i]));
            }
            sb.append("]");

            return truncateWithInfo(sb.toString(), MAX_ARGS_LENGTH);
        } catch (Exception e) {
            log.warn("Failed to sanitize args: {}", e.getMessage());
            return "[SANITIZE_ERROR]";
        }
    }

    /**
     * 清理返回结果
     *
     * @param result 返回结果
     * @return 清理后的字符串
     */
    public static String sanitizeResult(Object result) {
        if (result == null) {
            return "null";
        }

        try {
            return truncateWithInfo(sanitizeObject(result), MAX_RESULT_LENGTH);
        } catch (Exception e) {
            log.warn("Failed to sanitize result: {}", e.getMessage());
            return "[SANITIZE_ERROR]";
        }
    }

    /**
     * 清理单个对象
     *
     * @param obj 对象
     * @return 清理后的字符串
     */
    private static String sanitizeObject(Object obj) {
        if (obj == null) {
            return "null";
        }

        // 基本类型直接返回
        if (isPrimitiveOrWrapper(obj)) {
            return obj.toString();
        }

        // 字符串类型进行敏感信息过滤
        if (obj instanceof String) {
            return filterSensitiveInfo(obj.toString());
        }

        try {
            // 复杂对象转JSON后过滤
            String jsonStr = objectMapper.writeValueAsString(obj);
            return filterSensitiveInfo(jsonStr);
        } catch (JsonProcessingException e) {
            // JSON序列化失败，使用toString
            return filterSensitiveInfo(obj.toString());
        }
    }

    /**
     * 过滤敏感信息
     *
     * @param content 原始内容
     * @return 过滤后的内容
     */
    private static String filterSensitiveInfo(String content) {
        if (content == null || content.isEmpty()) {
            return content;
        }

        // 使用正则表达式替换敏感信息
        return SENSITIVE_PATTERN.matcher(content).replaceAll("$1=***");
    }


    /**
     * 判断是否为基本类型或包装类型
     *
     * @param obj 对象
     * @return 是否为基本类型
     */
    private static boolean isPrimitiveOrWrapper(Object obj) {
        return obj instanceof String ||
                obj instanceof Number ||
                obj instanceof Boolean ||
                obj instanceof Character ||
                obj.getClass().isPrimitive();
    }

    /**
     * 截断过长的内容并添加提示
     */
    private static String truncateWithInfo(String content, int maxLength) {
        if (content == null) {
            return "null";
        }

        if (content.length() <= maxLength) {
            return content;
        }

        int originalLength = content.length();
        int truncatedChars = originalLength - maxLength;

        return content.substring(0, maxLength) + "...TRUNCATED " + truncatedChars + " chars";
    }

    /**
     * 检查字段名是否为敏感字段
     *
     * @param fieldName 字段名
     * @return 是否为敏感字段
     */
    public static boolean isSensitiveField(String fieldName) {
        if (fieldName == null) {
            return false;
        }

        String lowerFieldName = fieldName.toLowerCase();
        return SENSITIVE_KEYWORDS.stream().anyMatch(lowerFieldName::contains);
    }
}
